{"name": "huntscreens", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "email:dev": "email dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "triggerdev": "pnpm dlx @trigger.dev/cli@latest dev", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:up": "drizzle-kit up", "index:all:embeddings": "tsx scripts/index.all.embeddings.ts", "db:trgm": "tsx scripts/add.trgm.extension.and.index.ts", "db:translate": "tsx scripts/add.multiple.langs.ts", "db:categories": "tsx scripts/add.product.categories.ts", "spider:stackshare": "tsx lib/spiders/stackshare.ts", "spider:startupstash": "tsx lib/spiders/startupstash.ts", "gemini:alternatives": "tsx lib/ai/gemini/alternatives.ts", "openai:alternatives": "tsx lib/ai/gemini/alternatives-openai.ts", "gemini:slug": "tsx lib/ai/gemini/slug.generator.ts", "gemini:tag": "tsx lib/ai/gemini/tag.generator.ts", "db:migrate:tables": "tsx scripts/migrate.tables.ts", "db:latest:top10": "tsx lib/emails/query.latest.top10.ts", "redis:migrate": "tsx scripts/migrate.redis.ts", "redis:aiintro": "tsx scripts/run.aiintro.ts", "qdrant:test": "tsx __tests__/qdrant.test.ts", "qdrant:index:products": "tsx scripts/index.products.vectors.ts", "db:intros": "tsx scripts/add.intros.ts", "db:migrate:seo": "tsx scripts/migrate.redis.to.db.ts", "db:unify:url": "tsx scripts/unify.url.ts", "db:redirect:ph": "tsx scripts/redirect.ph.ts", "db:slugs": "tsx scripts/add.slugs.ts"}, "dependencies": {"@ai-sdk/openai": "^0.0.60", "@anthropic-ai/sdk": "^0.27.3", "@google/generative-ai": "^0.17.1", "@logto/next": "^3.3.4", "@qdrant/js-client-rest": "^1.11.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.1", "@react-email/components": "0.0.21", "@react-email/render": "0.0.16", "@sentry/nextjs": "^8.30.0", "@trigger.dev/nextjs": "^3.0.2", "@trigger.dev/react": "^3.0.2", "@trigger.dev/resend": "^3.0.2", "@trigger.dev/sdk": "^3.0.2", "@xenova/transformers": "^2.17.2", "ai": "^3.3.43", "algoliasearch": "^4.24.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cookies-next": "^4.2.1", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.33.0", "he": "^1.2.0", "ioredis": "^5.4.1", "jotai": "^2.8.3", "lucide-react": "^0.397.0", "nanoid": "^5.0.7", "next": "14.2.5", "next-intl": "^3.19.0", "next-themes": "^0.3.0", "node-html-parser": "^6.1.13", "p-limit": "^6.1.0", "pg": "^8.12.0", "postgres": "^3.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-email": "2.1.5", "react-hotkeys-hook": "^4.5.0", "react-intersection-observer": "^9.13.0", "react-markdown": "^9.0.1", "resend": "^3.4.0", "rss": "^1.2.2", "screenshotone-api-sdk": "^1.1.10", "slugify": "^1.6.6", "swr": "^2.2.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "uuid": "^9.0.1", "vaul": "^0.9.1", "zod": "^3.22.3"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@testing-library/react": "^15.0.7", "@types/he": "^1.2.3", "@types/node": "^20", "@types/pg": "^8.11.6", "@types/react": "^18", "@types/react-dom": "^18", "@types/rss": "^0.0.32", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.3.0", "autoprefixer": "^10", "drizzle-kit": "^0.24.0", "encoding": "^0.1.13", "eslint": "^8", "eslint-config-next": "14.2.5", "jsdom": "^24.1.0", "postcss": "^8", "tailwindcss": "^3.4.4", "tailwindcss-dotted-background": "^1.1.0", "tsx": "^4.11.2", "typescript": "^5", "vitest": "^2.0.3"}, "trigger.dev": {"endpointId": "huntscreens-a5nE"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}